package com.teammanage.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.teammanage.entity.TeamMember;
import com.teammanage.enums.TeamRole;
import com.teammanage.mapper.TeamMemberMapper;

/**
 * TeamRoleTypeHandler 集成测试类
 * 
 * 测试 TypeHandler 在实际数据库操作中的工作情况
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TeamRoleTypeHandlerIntegrationTest {

    @Autowired
    private TeamMemberMapper teamMemberMapper;

    @Test
    public void testInsertAndSelectWithTypeHandler() {
        // 创建测试数据
        TeamMember teamMember = new TeamMember();
        teamMember.setTeamId(1L);
        teamMember.setAccountId(1L);
        teamMember.setRole(TeamRole.TEAM_CREATOR);
        teamMember.setIsActive(true);
        teamMember.setIsDeleted(false);

        // 插入数据
        int result = teamMemberMapper.insert(teamMember);
        assertEquals(1, result);
        assertNotNull(teamMember.getId());

        // 查询数据
        TeamMember retrieved = teamMemberMapper.selectById(teamMember.getId());
        assertNotNull(retrieved);
        assertEquals(TeamRole.TEAM_CREATOR, retrieved.getRole());
        assertEquals(1L, retrieved.getTeamId());
        assertEquals(1L, retrieved.getAccountId());
    }

    @Test
    public void testUpdateWithTypeHandler() {
        // 创建测试数据
        TeamMember teamMember = new TeamMember();
        teamMember.setTeamId(1L);
        teamMember.setAccountId(2L);
        teamMember.setRole(TeamRole.TEAM_MEMBER);
        teamMember.setIsActive(true);
        teamMember.setIsDeleted(false);

        // 插入数据
        teamMemberMapper.insert(teamMember);

        // 更新角色
        teamMember.setRole(TeamRole.TEAM_CREATOR);
        int updateResult = teamMemberMapper.updateById(teamMember);
        assertEquals(1, updateResult);

        // 验证更新结果
        TeamMember retrieved = teamMemberMapper.selectById(teamMember.getId());
        assertNotNull(retrieved);
        assertEquals(TeamRole.TEAM_CREATOR, retrieved.getRole());
    }

    @Test
    public void testQueryByTeamIdWithTypeHandler() {
        // 创建测试数据
        TeamMember creator = new TeamMember();
        creator.setTeamId(100L);
        creator.setAccountId(10L);
        creator.setRole(TeamRole.TEAM_CREATOR);
        creator.setIsActive(true);
        creator.setIsDeleted(false);
        teamMemberMapper.insert(creator);

        TeamMember member = new TeamMember();
        member.setTeamId(100L);
        member.setAccountId(11L);
        member.setRole(TeamRole.TEAM_MEMBER);
        member.setIsActive(true);
        member.setIsDeleted(false);
        teamMemberMapper.insert(member);

        // 查询团队成员
        var members = teamMemberMapper.findByTeamId(100L);
        assertEquals(2, members.size());

        // 验证角色正确转换
        boolean hasCreator = members.stream().anyMatch(m -> m.getRole() == TeamRole.TEAM_CREATOR);
        boolean hasMember = members.stream().anyMatch(m -> m.getRole() == TeamRole.TEAM_MEMBER);
        
        assertTrue(hasCreator, "应该包含团队创建者");
        assertTrue(hasMember, "应该包含团队成员");
    }
}
