<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teammanage.mapper.TeamMemberMapper">

    <!-- TeamMember结果映射 -->
    <resultMap id="TeamMemberResultMap" type="com.teammanage.entity.TeamMember">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="team_id" property="teamId" jdbcType="BIGINT"/>
        <result column="account_id" property="accountId" jdbcType="BIGINT"/>
        <result column="role" property="role" jdbcType="INTEGER" typeHandler="com.teammanage.config.TeamRoleTypeHandler"/>
        <result column="assigned_at" property="assignedAt" jdbcType="TIMESTAMP"/>
        <result column="last_access_time" property="lastAccessTime" jdbcType="TIMESTAMP"/>
        <result column="is_active" property="isActive" jdbcType="BOOLEAN"/>
        <result column="is_deleted" property="isDeleted" jdbcType="BOOLEAN"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据用户ID查询团队成员信息 -->
    <select id="findByAccountId" resultMap="TeamMemberResultMap">
        SELECT id, team_id, account_id, role, assigned_at, last_access_time, 
               is_active, is_deleted, created_at, updated_at
        FROM team_member 
        WHERE account_id = #{accountId} AND is_deleted = 0
    </select>

    <!-- 根据团队ID查询团队成员信息 -->
    <select id="findByTeamId" resultMap="TeamMemberResultMap">
        SELECT id, team_id, account_id, role, assigned_at, last_access_time, 
               is_active, is_deleted, created_at, updated_at
        FROM team_member 
        WHERE team_id = #{teamId} AND is_active = 1 AND is_deleted = 0
    </select>

    <!-- 根据团队ID和用户ID查询团队成员信息 -->
    <select id="findByTeamIdAndAccountId" resultMap="TeamMemberResultMap">
        SELECT id, team_id, account_id, role, assigned_at, last_access_time, 
               is_active, is_deleted, created_at, updated_at
        FROM team_member 
        WHERE team_id = #{teamId} AND account_id = #{accountId} AND is_deleted = 0
    </select>

</mapper>
